import React, { useState } from "react"
import { View, Text, StyleSheet, Image, ImageBackground } from "react-native"
import { useTranslation } from "react-i18next"
import { CustomPressable } from "src/componentsv2"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useInvestmentContext } from "../context/InvestmentContext"
import icBrik from "assets/imagesV2/ic_brik.png"
import icX from "assets/imagesV2/ic_x.png"
import icBookText from "assets/imagesV2/ic_book_text.png"
import bgInvestment from "assets/imagesV2/bg_investment.png"
import { IconButton } from "src/componentsv2/Button"

const DESCRIPTION_LIMIT = 120

const BasicView: React.FC = () => {
  const { t } = useTranslation()
  const { description } = useInvestmentContext()
  const [expanded, setExpanded] = useState(false)

  const isLongDescription =
    description && description.length > DESCRIPTION_LIMIT
  const displayText =
    !expanded && isLongDescription
      ? description.slice(0, DESCRIPTION_LIMIT) + "..."
      : description

  return (
    <ImageBackground source={bgInvestment} style={styles.background}>
      <View style={styles.container}>
        <Image source={icBrik} style={styles.icon} />
        <Text style={styles.title}>{t("Briky Land Tokens")}</Text>
        <View style={styles.content}>
          <Text style={styles.description}>{displayText}</Text>
          {isLongDescription && (
            <CustomPressable onPress={() => setExpanded(!expanded)}>
              <Text style={styles.viewMore}>
                {expanded ? t("View less") : t("View more")}
              </Text>
            </CustomPressable>
          )}
        </View>
        <View style={styles.row}>
          <IconButton
            style={styles.bookText}
            onPress={() => {
              // TODO
            }}
            icon={<Image source={icBookText} style={viewStyles.size12Icon} />}
          />
          <IconButton
            style={styles.x}
            onPress={() => {
              // TODO
            }}
            icon={<Image source={icX} style={viewStyles.size12Icon} />}
          />
        </View>
      </View>
    </ImageBackground>
  )
}

const styles = StyleSheet.create({
  background: {
    flex: 1,
    resizeMode: "cover",
    justifyContent: "center",
  },
  container: {
    paddingHorizontal: 16,
    paddingTop: 24,
    marginBottom: 16,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 12,
  },
  title: {
    ...textStyles.LSemiBold,
    marginBottom: 6,
  },
  content: {
    gap: 8,
  },
  description: {
    ...textStyles.SRegular,
    color: Colors.Neutral400,
  },
  viewMore: {
    marginTop: 4,
    ...textStyles.SSemiBold,
    color: Colors.Secondary300,
  },
  icon: {
    ...viewStyles.size32Icon,
    marginBottom: 10,
  },
  bookText: {
    padding: 6,
  },
  x: {
    marginStart: 4,
    padding: 6,
  },
})

export default BasicView
