import React from "react"
import {
  View,
  Text,
  Image,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ImageStyle,
} from "react-native"
import { useTranslation } from "react-i18next"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { PrimaryButton } from "src/componentsv2/Button"
import { ExpandView } from "src/components"
import { formatMoneyByDecimals } from "src/utils"
import iconFileBox from "assets/imagesV2/ic_file_box.png"
import iconBox from "assets/imagesV2/ic_box.png"
import { useCurrencies } from "../hooks/useCurrencies"
import ApplicationState from "./applicationstate/ApplicationState"
import { ApplicationStatus } from "src/api"

export interface ListItemCardProps {
  name: string
  imageUrl: string
  status?: ApplicationStatus
  price?: string
  decimals?: number
  currency?: string
  totalSupply?: string
  soldAmount?: string
  maxSellingAmount?: string
  onPress?: () => void
  style?: ViewStyle
}

/**
 * A common UI component for displaying items in MyEstate, TokenizationRequest, and Application tabs
 */
const ListItemCard: React.FC<ListItemCardProps> = ({
  name,
  imageUrl,
  status,
  price,
  decimals = 18,
  currency = "",
  totalSupply,
  soldAmount,
  maxSellingAmount,
  onPress,
  style,
}) => {
  const { t } = useTranslation()

  const symbol = useCurrencies(currency).tokenSymbol

  // Format values
  const formattedPrice = price ? formatMoneyByDecimals(price, decimals) : "0"
  const formattedTotalSupply = totalSupply
    ? formatMoneyByDecimals(totalSupply, decimals)
    : "0"
  const formattedSoldAmount = soldAmount
    ? formatMoneyByDecimals(soldAmount, decimals)
    : "0"
  const formattedMaxSellingAmount = maxSellingAmount
    ? formatMoneyByDecimals(maxSellingAmount, decimals)
    : "0"

  // Default image fallback
  const imageSource = { uri: imageUrl }

  // Handle press event
  const handlePress = () => {
    if (onPress) {
      onPress()
    }
  }

  return (
    <View style={[styles.cardContainer, style]}>
      <Image style={styles.thumbnail} source={imageSource} />

      <View style={styles.contentContainer}>
        <Text style={styles.title} numberOfLines={2} ellipsizeMode="tail">
          {name}
        </Text>

        {status && (
          <View style={styles.statusContainer}>
            <ApplicationState status={status} />
          </View>
        )}

        <View style={styles.detailsRow}>
          {price && (
            <View style={styles.priceInfoContainer}>
              <Image source={iconBox} style={viewStyles.size8Icon} />
              <Text style={styles.detailLabel}>
                {formattedPrice} {symbol}/NFT
              </Text>
            </View>
          )}

          {price && totalSupply && <View style={styles.dotSeparator} />}

          {totalSupply && (
            <View style={styles.nftInfoContainer}>
              <Image source={iconFileBox} style={viewStyles.size8Icon} />
              <Text style={styles.detailLabel}>{formattedTotalSupply} NFT</Text>
            </View>
          )}

          {soldAmount && maxSellingAmount && (
            <>
              <View style={styles.dotSeparator} />
              <View style={styles.nftInfoContainer}>
                <Image source={iconFileBox} style={viewStyles.size8Icon} />
                <Text style={styles.detailLabel}>
                  {formattedSoldAmount}/{formattedMaxSellingAmount}
                </Text>
              </View>
            </>
          )}
        </View>
        <ExpandView />
        <PrimaryButton
          title={t("Detail")}
          color={Colors.Neutral900}
          contentColor={Colors.PalleteWhite}
          width={165}
          onPress={handlePress}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  // List mode styles
  cardContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    paddingVertical: 12,
    paddingHorizontal: 0,
    gap: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.Neutral950,
  } as ViewStyle,

  thumbnail: {
    width: 107.84,
    height: 94,
    borderRadius: 4,
    alignSelf: "stretch",
    backgroundColor: Colors.Neutral300,
  } as ImageStyle,

  contentContainer: {
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "flex-start",
    padding: 0,
    gap: 6,
    flex: 1,
  } as ViewStyle,

  title: {
    ...textStyles.SMedium,
    color: Colors.white,
    letterSpacing: -0.4,
    alignSelf: "stretch",
  } as TextStyle,

  statusContainer: {
    marginBottom: 4,
  } as ViewStyle,

  detailsRow: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 4,
  } as ViewStyle,

  priceInfoContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 2,
  } as ViewStyle,

  nftInfoContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 2,
  } as ViewStyle,

  detailLabel: {
    ...textStyles.XSMedium,
    color: Colors.Neutral300,
    letterSpacing: -0.32,
  } as TextStyle,

  dotSeparator: {
    width: 2,
    height: 2,
    backgroundColor: Colors.Neutral500,
    borderRadius: 1,
    marginHorizontal: 2,
    alignSelf: "center",
  } as ViewStyle,

  // Grid mode styles
  gridItemContainer: {
    width: "48%",
    marginBottom: 16,
  } as ViewStyle,

  gridCard: {
    overflow: "hidden",
    backgroundColor: Colors.Neutral950,
    borderRadius: 8,
  } as ViewStyle,

  gridImage: {
    width: "100%",
    height: 120,
    resizeMode: "cover",
  } as ImageStyle,

  gridCardContent: {
    padding: 12,
  } as ViewStyle,

  gridName: {
    ...textStyles.MMedium,
    color: Colors.white,
    marginBottom: 8,
  } as TextStyle,

  gridDetails: {
    marginBottom: 12,
  } as ViewStyle,

  gridDetailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 4,
  } as ViewStyle,

  gridDetailLabel: {
    ...textStyles.bodyS,
    color: Colors.Neutral400,
  } as TextStyle,

  gridDetailValue: {
    ...textStyles.bodyS,
    color: Colors.white,
  } as TextStyle,

  gridActionButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    alignItems: "center",
  } as ViewStyle,

  gridActionButtonText: {
    ...textStyles.bodyS,
    color: Colors.PalleteBlack,
    fontWeight: "bold",
  } as TextStyle,
})

export default ListItemCard
