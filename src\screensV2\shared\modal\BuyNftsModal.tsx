import React from "react"
import {
  View,
  Text,
  Image,
  StyleSheet,
  Pressable,
  Modal,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from "react-native"

import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import iconClose from "assets/imagesV2/ic_close.png"
import { useTranslation } from "react-i18next"
import { Controller } from "react-hook-form"
import iconBNB from "assets/imagesV2/ic_bsc_network.png"
import { CustomPressable, InputField } from "src/componentsv2"
import { PrimaryButton } from "src/componentsv2/Button"
import { MarketplaceOffer } from "src/api"
import { useBuyNfts } from "./useBuyNfts"

interface BuyNftsModalProps {
  visible: boolean
  onClose: () => void
  offer: Omit<MarketplaceOffer, "seller">
}

const BuyNftsModal: React.FC<BuyNftsModalProps> = ({
  visible = false,
  onClose,
  offer,
}) => {
  const { t } = useTranslation()
  const {
    form,
    isLoading,
    isQuantityExceedsAvailable,
    maxNftAmount,
    availableAmount,
    formattedUnitPrice,
    totalFeeFormated,
    currencyData,
    handleSetMaxNftAmount,
    onSubmit,
    closeAndReset,
    decimals,
  } = useBuyNfts({ offer, onClose })

  const { isDivisible } = offer

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={closeAndReset}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        style={styles.keyboardAvoidingView}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.modalOverlay}>
            <View style={styles.modalContainer}>
              {/* Header */}
              <View style={styles.headerContainer}>
                <Text style={styles.headerTitle}>{t("Buy NFTs")}</Text>
                <Pressable onPress={closeAndReset}>
                  <Image source={iconClose} style={styles.closeButton} />
                </Pressable>
              </View>

              {/* Content */}
              <ScrollView style={styles.contentContainer}>
                <View style={styles.innerContent}>
                  {/* Network/Token Selection */}
                  <View style={styles.selectionRow}>
                    {/* Network Column */}
                    <View style={styles.selectionColumn}>
                      <Text style={styles.selectionLabel}>{t("Network")}</Text>
                      <View style={styles.dropdownContainer}>
                        <Pressable style={styles.dropdownButton}>
                          <View style={styles.iconTextGroup}>
                            <View style={styles.networkIconBackground}>
                              <Image
                                source={iconBNB}
                                style={[
                                  styles.networkIcon,
                                  styles.imagePlaceholder,
                                ]}
                              />
                            </View>
                            <Text style={styles.dropdownText}>
                              {t("BNB Chain")}
                            </Text>
                          </View>
                        </Pressable>
                      </View>
                    </View>

                    {/* Token Column */}
                    <View style={styles.selectionColumn}>
                      <Text style={styles.selectionLabel}>{t("Token")}</Text>
                      <View style={styles.dropdownContainer}>
                        <Pressable style={styles.dropdownButton}>
                          <View style={styles.iconTextGroup}>
                            <Image
                              source={{ uri: currencyData?.tokenImageUrl }}
                              style={styles.tokenIcon}
                            />
                            <Text style={styles.dropdownText}>
                              {currencyData?.tokenSymbol}
                            </Text>
                          </View>
                        </Pressable>
                      </View>
                    </View>
                  </View>

                  {/* Amount Input */}
                  <View style={styles.amountSection}>
                    <View style={styles.amountLabelRow}>
                      <Text style={styles.amountLabel}>
                        {t("Enter NFT amount")}
                      </Text>
                      <Text style={styles.amountLabel}>
                        {t("Available")}: {availableAmount}/{maxNftAmount} NFTs
                      </Text>
                    </View>
                    <Controller
                      control={form.control}
                      name="quantity"
                      render={({ field: { onChange, onBlur, value } }) => (
                        <View style={{ width: "100%" }}>
                          <InputField
                            style={styles.amountInput}
                            value={value?.toString() || ""}
                            onChangeText={onChange}
                            onBlur={onBlur}
                            type={"decimal"}
                            decimalPlaces={decimals}
                            placeholder="0.00"
                            inputMode="decimal"
                            disabled={isLoading || !isDivisible}
                          />
                          {isDivisible && (
                            <CustomPressable
                              style={styles.max}
                              onPress={() => {
                                handleSetMaxNftAmount(Number(availableAmount))
                              }}
                              enabled={!isLoading}
                            >
                              <Text style={styles.maxButtonText}>
                                {t("Max")}
                              </Text>
                            </CustomPressable>
                          )}
                        </View>
                      )}
                    />
                    {form.formState.errors.quantity && (
                      <Text style={styles.errorText}>
                        {form.formState.errors.quantity.message}
                      </Text>
                    )}
                  </View>

                  {/* Fee Details */}
                  <View style={styles.feeSection}>
                    {/* Total Fee Row */}
                    <View style={styles.totalFeeRow}>
                      <Text style={styles.totalFeeLabel}>{t("Total fee")}</Text>
                      <Text style={styles.totalFeeValue}>
                        {totalFeeFormated} {currencyData?.tokenSymbol}
                      </Text>
                    </View>

                    {/* Fee Detail Rows */}
                    <View style={styles.feeDetailRow}>
                      <Text style={styles.feeDetailLabel}>
                        {t("NFT amount")}
                      </Text>
                      <Text style={styles.feeDetailValue}>{maxNftAmount}</Text>
                    </View>
                    <View style={styles.feeDetailRow}>
                      <Text style={styles.feeDetailLabel}>
                        {t("Unit price")}
                      </Text>
                      <Text style={styles.feeDetailValue}>
                        {formattedUnitPrice} {currencyData?.tokenSymbol}
                      </Text>
                    </View>
                  </View>
                </View>
              </ScrollView>
              <PrimaryButton
                title={t("Buy Now")}
                onPress={form.handleSubmit(onSubmit)}
                isLoading={isLoading}
                height={38}
                style={styles.buyButton}
                enabled={!isQuantityExceedsAvailable}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </Modal>
  )
}

// --- Style Definitions ---
const styles = StyleSheet.create({
  // Modal overlay and container
  keyboardAvoidingView: {
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    width: 343,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    borderRadius: 12,
    flexDirection: "column",
    alignItems: "flex-start",
    backgroundColor: Colors.Neutral950,
    overflow: "hidden",
  },

  // Header
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    width: "100%",
    backgroundColor: Colors.Neutral950,
    borderBottomWidth: 1,
    borderBottomColor: Colors.Neutral900,
    alignSelf: "stretch",
  },
  headerTitle: {
    ...textStyles.LMedium,
    letterSpacing: -0.04 * 14,
    color: Colors.PalleteWhite,
  },
  closeButton: {
    width: 16,
    height: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  closeIcon: {
    width: "50%",
    height: "50%",
    borderWidth: 1.5,
    borderColor: Colors.Neutral500,
  },

  // Content
  contentContainer: {
    width: "100%",
    backgroundColor: Colors.Neutral950,
    borderBottomWidth: 1,
    borderBottomColor: Colors.Neutral900,
  },
  innerContent: {
    flexDirection: "column",
    alignItems: "flex-start",
    padding: 16,
    gap: 16,
    width: "100%",
  },

  // Selection Row (Network/Token)
  selectionRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 8,
    width: "100%",
  },
  selectionColumn: {
    flexDirection: "column",
    alignItems: "flex-start",
    gap: 4,
    flex: 1,
  },
  selectionLabel: {
    ...textStyles.MMedium,
    letterSpacing: -0.04 * 12,
    color: Colors.Neutral500,
  },
  dropdownContainer: {
    borderRadius: 8,
    alignSelf: "stretch",
    overflow: "hidden",
  },
  dropdownButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    gap: 10,
    height: 38,
    backgroundColor: Colors.Neutral900,
    borderRadius: 8,
    flex: 1,
    justifyContent: "space-between",
  },
  iconTextGroup: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  networkIconBackground: {
    width: 16,
    height: 16,
    borderRadius: 9999,
    backgroundColor: Colors.Neutral900,
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  networkIcon: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  tokenIcon: {
    width: 16,
    height: 16,
  },
  dropdownText: {
    ...textStyles.LMedium,
    letterSpacing: -0.04 * 14,
    color: Colors.PalleteWhite,
  },
  chevronIcon: {
    width: 16,
    height: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  chevronIconVector: {
    width: "50%",
    height: "25%",
    borderWidth: 1.5,
    borderColor: Colors.Neutral500,
  },

  // Amount Input
  amountSection: {
    flexDirection: "column",
    alignItems: "flex-start",
    gap: 8,
    width: "100%",
    alignSelf: "stretch",
  },
  amountLabelRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    height: 14,
    alignSelf: "stretch",
  },
  amountLabel: {
    ...textStyles.MMedium,
    letterSpacing: -0.04 * 12,
    color: Colors.Neutral500,
  },
  inputRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    flex: 1,
  },
  amountInput: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
    backgroundColor: Colors.PalleteBlack,
    padding: 0,
    flex: 1,
  },
  max: {
    position: "absolute",
    right: 8,
    transform: [{ translateY: 6 }],
  },
  maxButtonText: {
    ...textStyles.MMedium,
    paddingVertical: 5,
    paddingHorizontal: 8,
    borderRadius: 999,
    borderWidth: 1,
    color: Colors.Secondary500,
    borderColor: Colors.Secondary500,
  },
  errorText: {
    ...textStyles.SRegular,
    color: Colors.Danger500,
    marginTop: 4,
  },

  // Fee Details
  feeSection: {
    flexDirection: "column",
    alignItems: "flex-start",
    gap: 8,
    width: "100%",
    alignSelf: "stretch",
  },
  totalFeeRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    height: 17,
    alignSelf: "stretch",
  },
  feeLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  totalFeeLabel: {
    ...textStyles.LMedium,
    letterSpacing: -0.04 * 14,
    color: Colors.Neutral300,
  },
  feeChevronIcon: {
    width: 16,
    height: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  feeChevronVector: {
    width: "50%",
    height: "25%",
    borderWidth: 1.5,
    borderColor: Colors.Neutral300,
  },
  totalFeeValue: {
    ...textStyles.LSemiBold,
    letterSpacing: -0.04 * 14,
    color: Colors.PalleteWhite,
  },
  feeDetailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    width: "100%",
    height: 14,
    alignSelf: "stretch",
  },
  feeDetailLabel: {
    ...textStyles.MMedium,
    letterSpacing: -0.04 * 12,
    color: Colors.Neutral300,
  },
  feeDetailValue: {
    ...textStyles.MRegular,
    letterSpacing: -0.04 * 12,
    color: Colors.Neutral300,
  },

  // Footer
  footerContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
    width: "100%",
    backgroundColor: Colors.Neutral950,
    alignSelf: "stretch",
  },
  buyButton: {
    margin: 16,
    alignSelf: "stretch",
  },
  buyButtonText: {
    ...textStyles.LMedium,
    letterSpacing: -0.04 * 14,
    color: Colors.PalleteBlack,
    textAlign: "center",
  },

  // Helper Styles
  iconPlaceholder: {
    width: 16,
    height: 16,
  },
  imagePlaceholder: {
    backgroundColor: Colors.Neutral800,
  },
})

export default BuyNftsModal
