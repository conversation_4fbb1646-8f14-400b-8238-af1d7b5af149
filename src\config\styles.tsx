import { StyleSheet } from "react-native"
import Colors, { textColors } from "./colors"

const textStyles = StyleSheet.create({
  h3: {
    fontSize: 48,
    fontWeight: "400",
    color: textColors.textGray700,
    fontFamily: "RobotoRegular",
  },
  h4Large: {
    fontSize: 36,
    fontWeight: "600",
    color: textColors.textGray700,
    fontFamily: "RobotoBold",
  },
  titleL: {
    fontSize: 18,
    fontWeight: "500",
    lineHeight: 28,
    color: textColors.textBlack,
    fontFamily: "RobotoMedium",
  },
  body1: {
    fontSize: 16,
    fontWeight: "300",
    color: textColors.textGray700,
    fontFamily: "RobotoLigt",
  },
  bodyM: {
    fontSize: 14,
    fontWeight: "400",
    color: textColors.textBlack10,
    lineHeight: 20,
    fontFamily: "RobotoRegular",
  },
  bodyS: {
    fontSize: 12,
    fontWeight: "400",
    color: textColors.textBlack10,
    lineHeight: 16,
    fontFamily: "RobotoRegular",
  },
  labelL: {
    fontSize: 14,
    fontWeight: "500",
    color: textColors.textBlack10,
    fontFamily: "RobotoMedium",
  },
  labelM: {
    fontSize: 12,
    fontWeight: "500",
    color: textColors.textBlack10,
    fontFamily: "RobotoMedium",
  },
  labelS: {
    fontSize: 11,
    fontWeight: "500",
    lineHeight: 16,
    color: textColors.textBlack10,
    fontFamily: "RobotoMedium",
  },
  titleS: {
    fontSize: 14,
    fontWeight: "500",
    color: textColors.textBlack10,
    fontFamily: "RobotoMedium",
  },
  titleM: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.black7,
    fontFamily: "RobotoMedium",
    lineHeight: 24,
    letterSpacing: 0.15,
  },
  wHeadlineS: {
    fontSize: 24,
    fontWeight: "600",
    color: Colors.white,
    fontFamily: "RobotoMedium",
    lineHeight: 32,
  },
  // new styles
  size5XLRegular: {
    fontSize: 42,
    lineHeight: 50,
    fontWeight: "400",
    color: Colors.PalleteWhite,
    fontFamily: "InterRegular",
  },
  size5XLMedium: {
    fontSize: 42,
    lineHeight: 50,
    fontWeight: "500",
    color: Colors.PalleteWhite,
    fontFamily: "InterMedium",
  },
  size5XLSemiBold: {
    fontSize: 42,
    lineHeight: 50,
    fontWeight: "600",
    color: Colors.PalleteWhite,
    fontFamily: "InterSemiBold",
  },
  size5XLBold: {
    fontSize: 42,
    lineHeight: 50,
    fontWeight: "700",
    color: Colors.PalleteWhite,
    fontFamily: "InterBold",
  },
  size4XLRegular: {
    fontSize: 32,
    lineHeight: 38,
    fontWeight: "400",
    color: Colors.PalleteWhite,
    fontFamily: "InterRegular",
  },
  size4XLMedium: {
    fontSize: 32,
    lineHeight: 38,
    fontWeight: "500",
    color: Colors.PalleteWhite,
    fontFamily: "InterMedium",
  },
  size4XLSemiBold: {
    fontSize: 32,
    lineHeight: 38,
    fontWeight: "600",
    color: Colors.PalleteWhite,
    fontFamily: "InterSemiBold",
  },
  size4XLBold: {
    fontSize: 32,
    lineHeight: 38,
    fontWeight: "700",
    color: Colors.PalleteWhite,
    fontFamily: "InterBold",
  },
  size3XLRegular: {
    fontSize: 24,
    lineHeight: 29,
    fontWeight: "400",
    color: Colors.PalleteWhite,
    fontFamily: "InterRegular",
  },
  size3XLMedium: {
    fontSize: 24,
    lineHeight: 29,
    fontWeight: "500",
    color: Colors.PalleteWhite,
    fontFamily: "InterMedium",
  },
  size3XLSemiBold: {
    fontSize: 24,
    lineHeight: 29,
    fontWeight: "600",
    color: Colors.PalleteWhite,
    fontFamily: "InterSemiBold",
  },
  size3XLBold: {
    fontSize: 24,
    lineHeight: 29,
    fontWeight: "700",
    color: Colors.PalleteWhite,
    fontFamily: "InterBold",
  },
  size2XLRegular: {
    fontSize: 18,
    lineHeight: 22,
    fontWeight: "400",
    color: Colors.PalleteWhite,
    fontFamily: "InterRegular",
  },
  size2XLMedium: {
    fontSize: 18,
    lineHeight: 22,
    fontWeight: "500",
    color: Colors.PalleteWhite,
    fontFamily: "InterMedium",
  },
  size2XLSemiBold: {
    fontSize: 18,
    lineHeight: 22,
    fontWeight: "600",
    color: Colors.PalleteWhite,
    fontFamily: "InterSemiBold",
  },
  size2XLBold: {
    fontSize: 18,
    lineHeight: 22,
    fontWeight: "700",
    color: Colors.PalleteWhite,
    fontFamily: "InterBold",
  },
  XLRegular: {
    fontSize: 16,
    lineHeight: 19,
    fontWeight: "400",
    color: Colors.PalleteWhite,
    fontFamily: "InterRegular",
  },
  XLMedium: {
    fontSize: 16,
    lineHeight: 19,
    fontWeight: "500",
    color: Colors.PalleteWhite,
    fontFamily: "InterMedium",
  },
  XLSemiBold: {
    fontSize: 16,
    lineHeight: 19,
    fontWeight: "600",
    color: Colors.PalleteWhite,
    fontFamily: "InterSemiBold",
  },
  XLBold: {
    fontSize: 16,
    lineHeight: 19,
    fontWeight: "700",
    color: Colors.PalleteWhite,
    fontFamily: "InterBold",
  },
  LRegular: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: "400",
    color: Colors.PalleteWhite,
    fontFamily: "InterRegular",
  },
  LMedium: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: "500",
    color: Colors.PalleteWhite,
    fontFamily: "InterMedium",
  },
  LSemiBold: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: "600",
    color: Colors.PalleteWhite,
    fontFamily: "InterSemiBold",
  },
  LBold: {
    fontSize: 14,
    lineHeight: 17,
    fontWeight: "700",
    color: Colors.PalleteWhite,
    fontFamily: "InterBold",
  },
  MRegular: {
    fontSize: 12,
    lineHeight: 14,
    fontWeight: "400",
    color: Colors.PalleteWhite,
    fontFamily: "InterRegular",
  },
  MMedium: {
    fontSize: 12,
    lineHeight: 14,
    fontWeight: "500",
    color: Colors.PalleteWhite,
    fontFamily: "InterMedium",
  },
  MSemiBold: {
    fontSize: 12,
    lineHeight: 14,
    fontWeight: "600",
    color: Colors.PalleteWhite,
    fontFamily: "InterSemiBold",
  },
  MBold: {
    fontSize: 12,
    lineHeight: 14,
    fontWeight: "700",
    color: Colors.PalleteWhite,
    fontFamily: "InterBold",
  },
  SRegular: {
    fontSize: 10,
    lineHeight: 12,
    fontWeight: "400",
    color: Colors.PalleteWhite,
    fontFamily: "InterRegular",
  },
  SMedium: {
    fontSize: 10,
    lineHeight: 12,
    fontWeight: "500",
    color: Colors.PalleteWhite,
    fontFamily: "InterMedium",
  },
  SSemiBold: {
    fontSize: 10,
    lineHeight: 12,
    fontWeight: "600",
    color: Colors.PalleteWhite,
    fontFamily: "InterSemiBold",
  },
  SBold: {
    fontSize: 10,
    lineHeight: 12,
    fontWeight: "700",
    color: Colors.PalleteWhite,
    fontFamily: "InterBold",
  },
  XSRegular: {
    fontSize: 8,
    lineHeight: 10,
    fontWeight: "400",
    color: Colors.PalleteWhite,
    fontFamily: "InterRegular",
  },
  XSMedium: {
    fontSize: 8,
    lineHeight: 10,
    fontWeight: "500",
    color: Colors.PalleteWhite,
    fontFamily: "InterMedium",
  },
  XSSemiBold: {
    fontSize: 8,
    lineHeight: 10,
    fontWeight: "600",
    color: Colors.PalleteWhite,
    fontFamily: "InterSemiBold",
  },
  XSBold: {
    fontSize: 8,
    lineHeight: 10,
    fontWeight: "700",
    color: Colors.PalleteWhite,
    fontFamily: "InterBold",
  },
  size2XSRegular: {
    fontSize: 7,
    lineHeight: 8,
    fontWeight: "400",
    color: Colors.PalleteWhite,
    fontFamily: "InterRegular",
  },
  size2XSMedium: {
    fontSize: 7,
    lineHeight: 8,
    fontWeight: "500",
    color: Colors.PalleteWhite,
    fontFamily: "InterMedium",
  },
  size2XSSemiBold: {
    fontSize: 7,
    lineHeight: 8,
    fontWeight: "600",
    color: Colors.PalleteWhite,
    fontFamily: "InterSemiBold",
  },
  size2XSBold: {
    fontSize: 7,
    lineHeight: 8,
    fontWeight: "700",
    color: Colors.PalleteWhite,
    fontFamily: "InterBold",
  },
})

const viewStyles = StyleSheet.create({
  input: {
    height: 40,
    width: "100%",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    paddingHorizontal: 12,
    ...textStyles.MMedium,
  },
  bigIcon: {
    width: 40,
    height: 40,
  },
  icon: {
    width: 16,
    height: 16,
  },
  smallIcon: {
    width: 20,
    height: 20,
  },
  tinyIcon: {
    width: 16,
    height: 16,
  },
  //new icon styles
  size48Icon: {
    width: 48,
    height: 48,
  },
  size40Icon: {
    width: 40,
    height: 40,
  },
  size36Icon: {
    width: 36,
    height: 36,
  },
  size32Icon: {
    width: 32,
    height: 32,
  },
  size24Icon: {
    width: 24,
    height: 24,
  },
  size22Icon: {
    width: 22,
    height: 22,
  },
  size20Icon: {
    width: 20,
    height: 20,
  },
  size18Icon: {
    width: 18,
    height: 18,
  },
  size16Icon: {
    width: 16,
    height: 16,
  },
  size14Icon: {
    width: 14,
    height: 14,
  },
  size12Icon: {
    width: 12,
    height: 12,
  },
  size12x8Icon: {
    width: 12,
    height: 8,
  },
  size10Icon: {
    width: 10,
    height: 10,
  },
  size8Icon: {
    width: 8,
    height: 8,
  },
})

export { textStyles, viewStyles }
