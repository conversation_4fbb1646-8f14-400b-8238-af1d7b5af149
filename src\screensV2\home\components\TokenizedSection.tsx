import React from "react"
import { StyleSheet, View, ViewStyle } from "react-native"
import { Estate } from "src/api/types/estate"
import { useTokenized } from "./hooks/useTokenized"
import HeaderBar from "./HeaderBar"
import { GridTokenizedRenderItem } from "src/screensV2/shared/components"
import { calculateGridItemDimensions } from "src/utils/layout"

const { itemWidth, itemSpacing } = calculateGridItemDimensions({
  numColumns: 2,
})

interface TokenizedSectionProps {
  tokenizedEstates: Estate[]
  title: string
}

const TokenizedSection: React.FC<TokenizedSectionProps> = ({
  tokenizedEstates,
  title,
}) => {
  if (tokenizedEstates.length === 0) {
    return null
  }
  const { handleExplore } = useTokenized()

  return (
    <View>
      <HeaderBar
        title={title}
        isShowExplore={true}
        onPressExplore={handleExplore}
      />

      <GridTokenizedView tokenizedEstates={tokenizedEstates} />
    </View>
  )
}

interface GridTokenizedViewProps {
  tokenizedEstates: Estate[]
}

const GridTokenizedView: React.FC<GridTokenizedViewProps> = ({
  tokenizedEstates,
}) => {
  return (
    <View style={styles.container}>
      {tokenizedEstates.map((item, index) => {
        const itemStyle: ViewStyle = {
          ...styles.itemContainer,
          ...(index % 2 === 0 ? styles.itemMarginEnd : {}),
        }
        return (
          <GridTokenizedRenderItem
            key={item.id}
            estate={item}
            style={itemStyle}
          />
        )
      })}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  itemContainer: {
    width: itemWidth,
  },
  itemMarginEnd: {
    width: itemWidth,
    marginEnd: itemSpacing,
  },
})

export default TokenizedSection
