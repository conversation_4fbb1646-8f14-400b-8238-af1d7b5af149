import React from "react"
import { StyleSheet, Switch, Text, View, Image } from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import { PrimaryButton } from "src/componentsv2/Button"
import { CustomPressable, InputField } from "src/componentsv2"
import Colors from "src/config/colors"
import { Estate } from "src/api/types/estate"
import { useNewOffer } from "./hooks/useNewOffer"
import { BaseModal } from "src/componentsv2"
import { LabelView } from "components/common/LabelView"
import { Controller } from "react-hook-form"

interface NewOfferModalProps {
  estate: Estate
  isShow: boolean
  onClose: () => void
}

const NewOfferModal: React.FC<NewOfferModalProps> = ({
  isShow,
  onClose,
  estate,
}) => {
  const {
    form,
    isLoading,
    isBalancePositive,
    nftBalance,
    onSubmit,
    closeAndReset,
    handleSetMaxBalance,
    tokenSymbol,
    tokenImageUrl,
    t,
    decimals,
  } = useNewOffer({ estate, onClose })

  if (!form) return null

  return (
    <BaseModal
      visible={isShow}
      isShowCloseIcon={true}
      isDisableClose={isLoading}
      title={t("New Offer")}
      onClose={closeAndReset}
    >
      <View style={styles.modalContent}>
        <View style={styles.row}>
          <Text style={styles.labelFlex1}>{t("Unit price")}</Text>
          <Text style={styles.labelFlex1}>{t("Token")}</Text>
        </View>
        <View style={styles.unitPriceRow}>
          <InputField
            value={form.watch("unitPrice")}
            onChangeText={(val) => form.setValue("unitPrice", Number(val))}
            inputMode={"decimal"}
            type={"number"}
            style={styles.unitPrice}
            error={form.formState.errors.unitPrice?.message}
          />
          <View style={styles.currency}>
            <Image
              source={{ uri: tokenImageUrl }}
              style={viewStyles.size16Icon}
            />
            <Text style={styles.usdt}>{tokenSymbol}</Text>
          </View>
        </View>
        <View style={[styles.rowSpaceBetween, { marginVertical: 8 }]}>
          <LabelView label={t("Selling amount (NFT)")} />
          <Text
            style={styles.label}
          >{`${t("Balance")}: ${nftBalance} ${t("NFTs")}`}</Text>
        </View>
        <View>
          <Controller
            control={form.control}
            name="sellingAmount"
            render={({ field: { onChange, value } }) => (
              <InputField
                value={value?.toString() || ""}
                onChangeText={onChange}
                inputMode={"decimal"}
                type={"decimal"}
                decimalPlaces={decimals}
                error={form.formState.errors.sellingAmount?.message}
              />
            )}
          />
          <CustomPressable style={styles.max} onPress={handleSetMaxBalance}>
            <Text style={styles.maxText}>{t("Max")}</Text>
          </CustomPressable>
        </View>
        <View style={styles.rowSpaceBetween}>
          <Text style={styles.diviable}>{t("Diviable")}</Text>
          <Switch
            value={form.watch("isDiviable")}
            onValueChange={(val) => form.setValue("isDiviable", val)}
            trackColor={{
              false: Colors.Neutral900,
              true: Colors.Secondary500,
            }}
            thumbColor={
              form.watch("isDiviable") ? Colors.PalleteWhite : Colors.Neutral700
            }
          />
        </View>
        <PrimaryButton
          title={t("Borrow")}
          onPress={form.handleSubmit(onSubmit)}
          style={styles.marginTop8}
          isLoading={isLoading}
          height={38}
          borderRadius={8}
          textStyle={textStyles.LMedium}
          enabled={isBalancePositive}
        />
      </View>
    </BaseModal>
  )
}

const styles = StyleSheet.create({
  modalContent: {
    width: "100%",
  },
  rowSpaceBetween: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  row: {
    width: "100%",
    flexDirection: "row",
    marginTop: 16,
    marginBottom: 4,
  },
  labelFlex1: {
    ...textStyles.MMedium,
    color: Colors.Neutral500,
    flex: 1,
  },
  unitPriceRow: {
    width: "100%",
    flexDirection: "row",
  },
  unitPrice: {
    flex: 1,
  },
  diviable: {
    ...textStyles.MMedium,
    color: Colors.Neutral500,
  },
  marginTop8: {
    marginTop: 8,
  },
  currency: {
    height: 38,
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    marginStart: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    backgroundColor: Colors.Neutral900,
  },
  label: {
    ...textStyles.MMedium,
    color: Colors.Neutral500,
  },
  max: {
    position: "absolute",
    right: 8,
    transform: [{ translateY: 6 }],
  },
  usdt: {
    ...textStyles.LMedium,
    color: Colors.PalleteWhite,
    marginStart: 6,
  },
  maxText: {
    ...textStyles.MMedium,
    paddingVertical: 5,
    paddingHorizontal: 8,
    borderRadius: 999,
    borderWidth: 1,
    color: Colors.Secondary500,
    borderColor: Colors.Secondary500,
  },
})

export default NewOfferModal
