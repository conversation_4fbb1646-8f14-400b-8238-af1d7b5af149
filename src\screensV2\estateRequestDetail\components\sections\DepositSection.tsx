import React from "react"
import { DepositSectionItem } from "../../types"
import {
  DepositActionView,
  WithdrawSectionView,
  EstateValueView,
} from "src/screensV2/shared/components"
import { View, StyleSheet } from "react-native"
import Colors from "src/config/colors"
import { useCurrencies } from "src/screensV2/shared/hooks/useCurrencies"
interface DepositSectionProps {
  item: DepositSectionItem
}

const DepositSection: React.FC<DepositSectionProps> = ({ item }) => {
  const { estateRequest } = item
  const {
    maxSellingAmount,
    soldAmount,
    unitPrice,
    totalSupply,
    decimals,
    currency,
    state,
    id,
  } = estateRequest

  const { tokenSymbol } = useCurrencies(currency)

  return (
    <View style={styles.container}>
      <EstateValueView
        unitPrice={unitPrice}
        totalSupply={totalSupply}
        soldAmount={soldAmount}
        maxSellingAmount={maxSellingAmount}
        decimals={decimals}
        tokenSymbol={tokenSymbol ?? ""}
        state={state}
      />
      <DepositActionView
        state={state}
        tokenSymbol={tokenSymbol ?? ""}
        requestId={id}
        currencyId={currency}
        decimals={decimals}
        unitPrice={unitPrice}
        maxSellingAmount={maxSellingAmount}
        soldAmount={soldAmount}
      />
      <WithdrawSectionView
        requestId={id}
        state={state}
        currency={currency}
        decimals={decimals}
        unitPrice={unitPrice}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.Neutral950,
    borderRadius: 6,
    padding: 12,
    gap: 12,
  },
})

export default DepositSection
