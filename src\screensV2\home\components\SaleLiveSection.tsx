import React from "react"
import { StyleSheet, View, ViewStyle } from "react-native"
import { TokenizationRequest } from "src/api/types/estate"
import { useSaleLive } from "./hooks/useSaleLive"
import HeaderBar from "./HeaderBar"
import { GridSaleLiveRenderItem } from "src/screensV2/shared/components"
import { calculateGridItemDimensions } from "src/utils/layout"

const { itemWidth, itemSpacing } = calculateGridItemDimensions({
  numColumns: 2,
})

interface SaleLiveSectionProps {
  saleLive: TokenizationRequest[]
  title: string
}

const SaleLiveSection: React.FC<SaleLiveSectionProps> = ({
  saleLive,
  title,
}) => {
  if (saleLive.length === 0) {
    return null
  }
  const { handleExplore } = useSaleLive()

  return (
    <View>
      <HeaderBar
        title={title}
        isShowExplore={true}
        onPressExplore={handleExplore}
      />

      <GridSaleLiveView saleLive={saleLive} />
    </View>
  )
}

interface GridSaleLiveViewProps {
  saleLive: TokenizationRequest[]
}

const GridSaleLiveView: React.FC<GridSaleLiveViewProps> = ({ saleLive }) => {
  return (
    <View style={styles.container}>
      {saleLive.map((item, index) => {
        const itemStyle: ViewStyle = {
          ...styles.itemContainer,
          ...(index % 2 === 0 ? styles.itemMarginEnd : {}),
        }
        return (
          <GridSaleLiveRenderItem
            key={item.id}
            tokenizationRequest={item}
            style={itemStyle}
          />
        )
      })}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  itemContainer: {
    width: itemWidth,
  },
  itemMarginEnd: {
    width: itemWidth,
    marginEnd: itemSpacing,
  },
})

export default SaleLiveSection
