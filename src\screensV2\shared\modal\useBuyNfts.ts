import { useState } from "react"
import { useTranslation } from "react-i18next"
import { maxUint256, parse<PERSON>bi } from "viem"
import { useAccount, useWriteContract } from "wagmi"
import { useEthersProvider } from "hooks"
import { erc20Abi, useErc20Allowance } from "src/api/contracts"
import { marketplaceAbi } from "src/api/contracts/marketplace"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { CONTRACT_ADDRESS_MARKETPLACE } from "src/config/env"
import {
  formatCurrencyByDecimals,
  formatNumericByDecimals,
  formatCurrency,
} from "utils"
import Logger from "src/utils/logger"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import { MarketplaceOffer } from "src/api"
import { useCurrencies } from "../hooks/useCurrencies"
import { formatNumericByDecimalsToNumber } from "src/utils/numberExt"

const logger = new Logger({ tag: "useBuyNfts" })

const formSchema = z.object({
  quantity: z.coerce.number().min(0.000000000000000001),
})
type Payload = z.infer<typeof formSchema>

interface UseBuyNftsProps {
  offer: Omit<MarketplaceOffer, "seller">
  onClose: () => void
}

export const useBuyNfts = ({ offer, onClose }: UseBuyNftsProps) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)
  const { address } = useAccount()

  const {
    id,
    estate: { decimals, id: estateId },
    currency: currencyId,
    isDivisible,
    unitPrice,
    sellingAmount,
    soldAmount,
  } = offer

  const currencyData = useCurrencies(currencyId)
  const maxNftAmount = formatNumericByDecimals(sellingAmount, decimals)
  const availableAmount =
    formatNumericByDecimalsToNumber(sellingAmount, decimals) -
    formatNumericByDecimalsToNumber(soldAmount, decimals)
  const formattedUnitPrice = formatCurrencyByDecimals(unitPrice, decimals)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    mode: "onTouched",
    defaultValues: {
      quantity: isDivisible
        ? 1
        : formatNumericByDecimalsToNumber(sellingAmount, decimals),
    },
  })

  const quantity = form.watch("quantity")
  const isQuantityExceedsAvailable = quantity > Number(availableAmount)

  const totalFeeFormated = formatCurrency(
    formatNumericByDecimalsToNumber(unitPrice, decimals) * quantity
  )

  const ethersProvider = useEthersProvider()

  const currencyAllowanceWei = useErc20Allowance(
    address as string,
    currencyId as `0x${string}`,
    CONTRACT_ADDRESS_MARKETPLACE
  )

  const { writeContractAsync } = useWriteContract()

  const onSubmit = async (data: Payload) => {
    if (isLoading || !ethersProvider) return
    if (
      data.quantity >
      formatNumericByDecimalsToNumber(sellingAmount, decimals) -
        formatNumericByDecimalsToNumber(soldAmount, decimals)
    ) {
      form.setError("quantity", {
        type: "custom",
        message: `You can only buy up to ${Number(sellingAmount) - Number(soldAmount)} NFTs`,
      })
      return
    }

    setIsLoading(true)
    try {
      if (
        currencyAllowanceWei <
        (BigInt(unitPrice) *
          BigInt(Math.round(data.quantity * Math.pow(10, decimals)))) /
          BigInt(Math.pow(10, decimals))
      ) {
        const txHash = await writeContractAsync({
          address: currencyId as `0x${string}`,
          abi: erc20Abi,
          functionName: "approve",
          args: [CONTRACT_ADDRESS_MARKETPLACE, maxUint256],
        })
        const receipt = await ethersProvider.waitForTransaction(txHash)
        if (receipt.status !== 1) {
          showError(t("Fail to approve currency"))
          throw new Error("Fail to approve currency")
        }
      }
      let txHash: string
      if (isDivisible) {
        const quantityInSmallestUnit = Math.round(
          data.quantity * Math.pow(10, decimals)
        )

        txHash = await writeContractAsync({
          address: CONTRACT_ADDRESS_MARKETPLACE,
          abi: parseAbi(["function buyToken(uint256, uint256, uint256)"]),
          functionName: "buyToken",
          args: [BigInt(id), BigInt(estateId), BigInt(quantityInSmallestUnit)],
        })
      } else {
        txHash = await writeContractAsync({
          address: CONTRACT_ADDRESS_MARKETPLACE,
          abi: marketplaceAbi,
          functionName: "buyToken",
          args: [BigInt(id), BigInt(estateId)],
        })
      }

      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Buy NFT success") + ". " + t("Data will be updated in few seconds")
        )
      } else {
        logger.error("Buy NFT failed", receipt.transactionHash)
        showError(t("Buy NFT failed"))
      }
    } catch (e: any) {
      logger.error("Buy NFT failed", e)
      showError(t("Buy NFT failed"))
    } finally {
      closeAndReset()
    }
  }

  const closeAndReset = () => {
    form.reset()
    setIsLoading(false)
    onClose()
  }

  const handleSetMaxNftAmount = (maxAvailableAmount: number) => {
    form.setValue("quantity", maxAvailableAmount)
  }

  return {
    form,
    isLoading,
    isQuantityExceedsAvailable,
    maxNftAmount,
    availableAmount,
    formattedUnitPrice,
    totalFeeFormated,
    currencyData,
    handleSetMaxNftAmount,
    onSubmit,
    closeAndReset,
    decimals,
  }
}
