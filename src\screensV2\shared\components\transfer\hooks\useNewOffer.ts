import { useEffect, useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useTranslation } from "react-i18next"
import { useQuery } from "@tanstack/react-query"
import { useAccount, useWriteContract } from "wagmi"
import { parseEther } from "@ethersproject/units"
import { ContractFunctionExecutionError } from "viem"
import { getCurrencies } from "src/api"
import { useEstateTokenBalance } from "src/api/contracts/stake-token"
import { useCollectionIsApprovedForAll } from "src/api/contracts/collection"
import { collectionAbi } from "src/api/contracts/collection"
import { marketplaceAbi } from "src/api/contracts/marketplace"
import {
  CONTRACT_ADDRESS_ESTATE_TOKEN,
  CONTRACT_ADDRESS_MARKETPLACE,
} from "src/config/env"
import { showError, showSuccessWhenCallContract } from "src/utils/toast"
import { queryClient } from "src/api/query"
import QueryKeys from "src/config/queryKeys"
import Logger from "src/utils/logger"
import { useEthersProvider } from "hooks"
import type { Estate } from "src/api/types/estate"
import type { Currency } from "src/api/types/currency"
import { TFunction } from "i18next"
import { useCurrencies } from "src/screensV2/shared/hooks/useCurrencies"

const logger = new Logger({ tag: "NewOfferModal" })

const useFormSchema = (nftBalance: string, t: TFunction) =>
  z.object({
    currencyId: z.string(),
    unitPrice: z.number().min(1, {
      message: t("Please input a valid unit price greater than 0"),
    }),
    sellingAmount: z.coerce
      .number()
      .min(0.000000000000000001, {
        message: t("Please input a valid selling amount greater than 0"),
      })
      .refine((val) => Number(nftBalance) >= val, {
        message: t("Selling amount cannot exceed your NFT balance"),
      }),
    isDiviable: z.boolean(),
  })

type Payload = {
  currencyId: string
  unitPrice: number
  sellingAmount: number
  isDiviable: boolean
}

export function useNewOffer({
  estate,
  onClose,
}: {
  estate: Estate
  onClose: () => void
}) {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)
  const { address } = useAccount()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()
  const {
    id,
    decimals,
    tokenizationRequest: { currency },
  } = estate

  const { data: currencies = [] } = useQuery<Currency[]>({
    queryKey: QueryKeys.CURRENCY.LIST,
    queryFn: () => getCurrencies(),
  })

  const { value: nftBalance, queryKey } = useEstateTokenBalance(
    address as `0x${string}`,
    id
  )

  const isBalancePositive = Number(nftBalance) > 0

  const { tokenSymbol, tokenImageUrl } = useCurrencies(currency)

  const formSchema = useFormSchema(nftBalance, t)
  const form = useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      currencyId: currencies[0]?.currency,
      unitPrice: 0,
      sellingAmount: 0,
      isDiviable: false,
    },
  })

  const isApprovedForAll = useCollectionIsApprovedForAll(
    address as string,
    CONTRACT_ADDRESS_MARKETPLACE
  )

  useEffect(() => {
    if (currencies.length > 0 && !form.getValues("currencyId")) {
      form.setValue("currencyId", currencies[0].symbol.toLowerCase())
    }
  }, [currencies])

  const closeAndReset = () => {
    form.reset()
    setIsLoading(false)
    onClose()
  }

  const onSubmit = async (data: Payload) => {
    if (
      isLoading ||
      !ethersProvider ||
      !Number(id) ||
      !data.unitPrice ||
      !data.sellingAmount
    )
      return

    setIsLoading(true)
    try {
      if (!isApprovedForAll) {
        const txHash = await writeContractAsync({
          address: CONTRACT_ADDRESS_ESTATE_TOKEN,
          abi: collectionAbi,
          functionName: "setApprovalForAll",
          args: [CONTRACT_ADDRESS_MARKETPLACE, true],
        })
        const receipt = await ethersProvider.waitForTransaction(txHash)
        if (receipt.status !== 1) {
          throw new Error("Failed to set approval for all")
        }
      }

      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MARKETPLACE,
        abi: marketplaceAbi,
        functionName: "listToken",
        args: [
          BigInt(id),
          BigInt(Math.round(data.sellingAmount * Math.pow(10, decimals))),
          parseEther(data.unitPrice.toString()).toBigInt(),
          data.currencyId,
          data.isDiviable,
        ],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        await queryClient.invalidateQueries({ queryKey })
        showSuccessWhenCallContract(
          t("Create offer success") +
            ". " +
            t("Data will be updated in few seconds")
        )
      } else {
        throw new Error(t("Create offer failed"))
      }
    } catch (e: any) {
      if (e instanceof ContractFunctionExecutionError) {
        logger.error("Contract execution error details", {
          cause: e.cause,
          metaMessages: e.cause.metaMessages,
          docsPath: e.cause.docsPath,
        })
      }
      logger.error("Create offer failed", e)
      const errorMessage = `${t("Create offer failed")} ${e?.toString()}`
      showError(errorMessage)
    } finally {
      closeAndReset()
    }
  }

  const handleSetMaxBalance = () => {
    form.setValue("sellingAmount", 10)
  }

  return {
    form,
    isLoading,
    isBalancePositive,
    nftBalance,
    currencies,
    onSubmit,
    closeAndReset,
    handleSetMaxBalance,
    tokenSymbol,
    tokenImageUrl,
    t,
    decimals,
  }
}
