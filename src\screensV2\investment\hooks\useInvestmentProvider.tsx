import { useState } from "react"
import { InvestmentContextState } from "src/screensV2/investment/context/types"
import useOracle from "./useInvestment"
import { InvestmentRoundType } from "src/api"
import { useInvestmentSections } from "./useInvestmentSections"
import { useTranslation } from "react-i18next"

export const useInvestmentProvider = (): InvestmentContextState => {
  const { t } = useTranslation()
  // TODO: fake
  const description =
    "Căn hộ nằm trên tầng cao của một tòa chung cư hiện đại, với thiết kế mở và nội thất tối giản mang phong cách châu Âu. Phòng khách rộng rãi được kết nối liền mạch với khu bếp và bàn ăn, tạo cảm giác ấm cúng và tiện nghi. <PERSON><PERSON> sáng tự nhiên tràn ngập qua những ô cửa kính lớn, gi<PERSON><PERSON> không gian luôn sáng sủa và thoáng đãng. Phòng ngủ được bố trí gọn gàng với gam màu trung tính, mang lại cảm giác thư giãn sau một ngày làm việc. Ban công nhỏ xinh nhìn ra thành phố, là nơi lý tưởng để nhâm nhi cà phê sáng hoặc đọc sách vào buổi chiều."
  const roundNameMap: Record<string, string> = {
    BACKER: t("Backer Round"),
    SEED: t("Seed Round"),
    PRIVATE_SALE_1: t("Private Sale 1"),
    PRIVATE_SALE_2: t("Private Sale 2"),
    PUBLIC_SALE: t("Public Sale"),
  }

  const { oracle, isInvestmentLoading, oracleError } = useOracle()
  const investmentRounds = oracle?.investment.rounds || []
  const [selectedRound, setSelectedRound] = useState<
    InvestmentRoundType | undefined
  >(undefined)
  const { sections } = useInvestmentSections()

  return {
    sections,
    isInvestmentLoading,
    investmentError: oracleError,
    investmentRounds,
    description,
    selectedRound,
    roundNameMap,
    setSelectedRound,
  }
}
