import { useTranslation } from "react-i18next"
import { useQueries } from "@tanstack/react-query"
import { useMemo } from "react"
import {
  Estate,
  getHomeLeaderboard,
  getHomeRecentOffers,
  MarketplaceOffer,
  TokenizationRequest,
} from "src/api"
import { QueryKeys } from "src/config/queryKeys"

// Định nghĩa enum cho các section type
export enum SectionType {
  FEATURED_ESTATES = "featuredEstates",
  HOT_DEALS = "hotDeals",
  SALE_LIVE = "saleLive",
  UPCOMING = "upcoming",
  TOKENIZED = "tokenized",
  RECENT_OFFERS = "recentOffers",
}

// Định nghĩa kiểu dữ liệu cho các section
export interface Section {
  type: SectionType
  title: string
  data: any[]
}

// Kiểu dữ liệu cho tất cả dữ liệu trang chủ
interface HomeData {
  featuredEstates: {
    data: Estate[]
  }
  hotDeals: {
    data: TokenizationRequest[]
  }
  saleLive: {
    data: TokenizationRequest[]
  }
  upcoming: {
    data: TokenizationRequest[]
  }
  tokenized: {
    data: Estate[]
  }
  recentOffers: {
    data: MarketplaceOffer[]
  }
}

// Hook duy nhất để lấy tất cả dữ liệu trang chủ
export const useHomeData = () => {
  const { t } = useTranslation()

  // Sử dụng useQueries với option combine để gọi nhiều API cùng lúc và kết hợp kết quả
  const results = useQueries({
    queries: [
      // Query 1: Lấy dữ liệu leaderboard
      {
        queryKey: QueryKeys.ESTATE.HOME_LEADERBOARD,
        queryFn: () => getHomeLeaderboard(),
      },
      // Query 2: Lấy dữ liệu recent offers
      {
        queryKey: QueryKeys.MARKETPLACE.HOME_RECENT_OFFERS,
        queryFn: () => getHomeRecentOffers(),
      },
    ],
  })

  // Kết hợp kết quả từ các query thành một đối tượng HomeData
  const [leaderboardResult, recentOffersResult] = results

  const leaderboardData = leaderboardResult.data

  const data: HomeData = useMemo(
    () => ({
      featuredEstates: {
        data: leaderboardData?.featured || [],
      },
      hotDeals: {
        data: leaderboardData?.hot_deal || [],
      },
      saleLive: {
        data: leaderboardData?.sale_live || [],
      },
      upcoming: {
        data: leaderboardData?.upcoming || [],
      },
      tokenized: {
        data: leaderboardData?.tokenized || [],
      },
      recentOffers: {
        data: recentOffersResult?.data || [],
      },
    }),
    [leaderboardData, recentOffersResult]
  )

  const isLoading = leaderboardResult.isLoading || recentOffersResult.isLoading
  const isAllError = leaderboardResult.error && recentOffersResult.error

  // Tạo danh sách các section từ dữ liệu
  const sections: Section[] = useMemo(
    () => [
      {
        type: SectionType.FEATURED_ESTATES,
        title: t("Featured Estates"),
        data: data.featuredEstates.data,
      },
      {
        type: SectionType.HOT_DEALS,
        title: t("Hot Deals"),
        data: data.hotDeals.data,
      },
      {
        type: SectionType.SALE_LIVE,
        title: t("Sale Live"),
        data: data.saleLive.data,
      },
      {
        type: SectionType.UPCOMING,
        title: t("Upcoming"),
        data: data.upcoming.data,
      },
      {
        type: SectionType.TOKENIZED,
        title: t("Tokenized"),
        data: data.tokenized.data,
      },
      {
        type: SectionType.RECENT_OFFERS,
        title: t("Recent Offers"),
        data: data.recentOffers.data,
      },
    ],
    [data, t]
  )

  const isAllHasNoData = useMemo(
    () =>
      !isLoading &&
      !isAllError &&
      sections?.length > 0 &&
      sections.every(
        (section) => Array.isArray(section?.data) && section.data.length === 0
      ),
    [isLoading, isAllError, sections]
  )

  return {
    sections,
    isLoading,
    isAllError,
    isAllHasNoData,
  }
}
