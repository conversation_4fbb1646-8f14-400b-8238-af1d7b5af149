import { useTranslation } from "react-i18next"
import { TokenizationRequestState } from "src/api/types"
import { formatCurrencyByDecimals, fixedPointMultiply } from "src/utils"

interface UseWithdrawDataProps {
  depositedAmount: bigint
  state: TokenizationRequestState
  tokenSymbol: string
  decimals: number
  unitPrice: string
}

export function useWithdrawData({
  depositedAmount,
  state,
  tokenSymbol,
  decimals,
  unitPrice,
}: UseWithdrawDataProps) {
  const { t } = useTranslation()

  const isWithdrawNFTs =
    depositedAmount > 0 && state === TokenizationRequestState.CONFIRMED
  const isWithdrawDeposit =
    depositedAmount > 0 &&
    (state === TokenizationRequestState.INSUFFICIENT_SOLD_AMOUNT ||
      state === TokenizationRequestState.EXPIRED ||
      state === TokenizationRequestState.CANCELLED)
  const isCantWithdraw = !isWithdrawNFTs && !isWithdrawDeposit

  const formatDepositedAmount = formatCurrencyByDecimals(
    depositedAmount.toString(),
    decimals
  )
  const depositValue = fixedPointMultiply(
    BigInt(unitPrice),
    depositedAmount,
    decimals
  )
  const displayDeposited = isWithdrawDeposit
    ? `${formatCurrencyByDecimals(depositValue.toString(), decimals)} ${tokenSymbol}`
    : `${Number(formatDepositedAmount)}x ${t("NFTs")}`

  const displayButtonText = isWithdrawNFTs
    ? `${t("Withdraw")} ${t("NFTs")}`
    : isWithdrawDeposit
      ? `${t("Withdraw")} ${tokenSymbol}`
      : `${t("Cant withdraw")}`

  const displayWithdrawWarningText = `${t(
    "Since the transfer period has expired, you will be allowed to withdraw your"
  )} ${tokenSymbol} ${t("deposit")}`

  return {
    isWithdrawNFTs,
    isWithdrawDeposit,
    isCantWithdraw,
    displayDeposited,
    displayButtonText,
    displayWithdrawWarningText,
  }
}
