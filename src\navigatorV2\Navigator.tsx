import React from "react"
import { LinkingOptions, NavigationContainer } from "@react-navigation/native"
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import { createStackNavigator } from "@react-navigation/stack"
import { Image } from "react-native"
import * as Routes from "./routes"
import CreateNFTScreen from "src/screensV2/createnft/CreateNFTScreen"
import HomeScreen from "src/screensV2/home"
import EstateScreen from "src/screensV2/estate/EstateScreen"
import ProfileScreen from "src/screensV2/profile/ProfileScreen"
import SettingsScreen from "src/screensV2/setting/SettingsScreen"
import OfficesScreen from "../screensV2/setting/office/OfficesScreen"
import ContactScreen from "../screensV2/setting/contact/ContactScreen"
import ReferencesScreen from "../screensV2/setting/references/ReferencesScreen"
import SelectLanguageScreen from "../screensV2/setting/language/SelectLanguageScreen"
import VerifyAccountScreen from "../screensV2/verifyaccount/VerifyAccountScreen"
import EditProfileScreen from "../screensV2/editprofile/EditProfileScreen"
import { EstateDetailScreen } from "src/screensV2/estateDetail"
import { EstateRequestDetailScreen } from "src/screensV2/estateRequestDetail"
import { ApplicationDetailScreen } from "src/screensV2/applicationDetail"
import { RootStackParamList, TabParamList } from "./rootStackParamsList"
import { useTranslation } from "react-i18next"

// Import icons for tab bar
import icHome from "assets/imagesV2/ic_laptop.png"
import icMenuProfile from "assets/imagesV2/ic_user_round.png"
import icCompass from "assets/imagesV2/ic_compass.png"
import icSetting from "assets/imagesV2/ic_settings.png"
import icCoins from "assets/imagesV2/ic_coins.png"
import { MainTabHeader } from "src/componentsv2"
import GuestProfileScreen from "../screensV2/profile/GuestProfileScreen"
import Colors from "../config/colors"
import InvestmentScreen from "../screensV2/investment/InvestmentScreen"

const prefix = "exp://"
const linking: LinkingOptions<RootStackParamList> = {
  prefixes: [prefix],
  config: {
    screens: {
      Tabs: {
        screens: {
          [Routes.HOME_TAB]: "home",
          [Routes.PROFILE_TAB]: "profile",
          [Routes.SETTINGS_TAB]: "settings",
          [Routes.ESTATE_TAB]: "estate",
        },
      },
    },
  },
}

const Tab = createBottomTabNavigator<TabParamList>()
const Stack = createStackNavigator<RootStackParamList>()

const Navigator: React.FC = () => {
  return (
    <NavigationContainer linking={linking}>
      <Stack.Navigator
        screenOptions={{
          header: () => <MainTabHeader />,
          cardStyle: { backgroundColor: Colors.PalleteBlack },
          animationEnabled: false,
        }}
      >
        <Stack.Screen name="Tabs" component={TabNavigator} />

        <Stack.Screen
          name={Routes.ESTATE_REQUEST_DETAIL}
          component={EstateRequestDetailScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={Routes.ESTATE_DETAIL}
          component={EstateDetailScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={Routes.CREATE_NFT}
          component={CreateNFTScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={Routes.VERIFY_ACCOUNT}
          component={VerifyAccountScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={Routes.EDIT_PROFILE}
          component={EditProfileScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={Routes.APPLICATION_DETAIL}
          component={ApplicationDetailScreen}
          options={{
            headerShown: false,
          }}
        />

        <Stack.Screen
          name={Routes.SELECT_LANGUAGE}
          component={SelectLanguageScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={Routes.REFERENCES}
          component={ReferencesScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={Routes.CONTACT}
          component={ContactScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={Routes.OFFICES}
          component={OfficesScreen}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={Routes.GUEST_PROFILE}
          component={GuestProfileScreen}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  )
}

// Tab Navigator
const TabNavigator: React.FC = () => {
  const { t } = useTranslation()

  return (
    <Tab.Navigator
      initialRouteName={Routes.HOME_TAB}
      screenOptions={{
        tabBarActiveTintColor: "#FFFFFF",
        tabBarInactiveTintColor: "#666666",
        tabBarStyle: {
          backgroundColor: "#000000",
          borderTopColor: "#333333",
          height: 60,
          paddingBottom: 10,
        },
        headerShown: false,
      }}
    >
      <Tab.Screen
        name={Routes.HOME_TAB}
        component={HomeScreen}
        options={{
          tabBarLabel: t("Home"),
          tabBarIcon: ({ color, size }) => (
            <Image
              source={icHome}
              style={{ width: size, height: size, tintColor: color }}
            />
          ),
        }}
      />
      <Tab.Screen
        name={Routes.ESTATE_TAB}
        component={EstateScreen}
        options={{
          tabBarLabel: t("Explore"),
          tabBarIcon: ({ color, size }) => (
            <Image
              source={icCompass}
              style={{ width: size, height: size, tintColor: color }}
            />
          ),
        }}
      />
      <Tab.Screen
        name={Routes.PROFILE_TAB}
        component={ProfileScreen}
        options={{
          tabBarLabel: t("My Profile"),
          tabBarIcon: ({ color, size }) => (
            <Image
              source={icMenuProfile}
              style={{ width: size, height: size, tintColor: color }}
            />
          ),
        }}
      />
      <Tab.Screen
        name={Routes.TOKENS_TAB}
        component={InvestmentScreen}
        options={{
          tabBarLabel: t("Tokens"),
          tabBarIcon: ({ color, size }) => (
            // TODO
            <Image
              source={icCoins}
              style={{ width: size, height: size, tintColor: color }}
            />
          ),
        }}
      />
      <Tab.Screen
        name={Routes.SETTINGS_TAB}
        component={SettingsScreen}
        options={{
          tabBarLabel: t("Settings"),
          tabBarIcon: ({ color, size }) => (
            <Image
              source={icSetting}
              style={{ width: size, height: size, tintColor: color }}
            />
          ),
        }}
      />
    </Tab.Navigator>
  )
}

export default Navigator
