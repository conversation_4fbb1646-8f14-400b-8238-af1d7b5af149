import React from "react"
import { View } from "react-native"
import HotDealR<PERSON>Item from "./HotDealRenderItem"
import HeaderB<PERSON> from "./HeaderBar"
import { TokenizationRequest } from "src/api/types/estate"

interface HotDealsSectionProps {
  hotDeals: TokenizationRequest[]
  title: string
}

const HotDealsSection: React.FC<HotDealsSectionProps> = ({
  hotDeals,
  title,
}) => {
  if (hotDeals.length === 0) {
    return null
  }
  return (
    <View>
      <HeaderBar title={title} isShowExplore={false} />
      <ListHotDealsView hotDeals={hotDeals} />
    </View>
  )
}

interface ListHotDealsViewProps {
  hotDeals: TokenizationRequest[]
}

const ListHotDealsView: React.FC<ListHotDealsViewProps> = ({ hotDeals }) => {
  return (
    <View>
      {hotDeals.map((item) => (
        <HotDealRenderItem key={item.id} tokenizationRequest={item} />
      ))}
    </View>
  )
}

export default HotDealsSection
